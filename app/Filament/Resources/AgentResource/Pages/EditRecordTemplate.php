<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Models\Agent;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Closure;
use Filament\Forms;

use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Concerns\Translatable;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Actions\Action;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;
use App\Filament\Pages\App\Analytics\AgentAnalytics;
use App\Filament\Resources\AgentResource;
use App\Filament\Resources\AgentResource\Actions\LocaleSwitcher;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Filament\Traits\HasFields;
use App\Jobs\TranslateModelJob;
use App\Models\AgentQuestion;
use App\Services\Formatter;
use Carbon\Carbon;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Toggle;
use Livewire\Component;

class EditRecordTemplate extends BaseEditRecord
{
    use HasFields;
    use Translatable;

    protected static string $resource = AgentResource::class;

    protected static ?string $contributeTemplate = 'components.app.contribute-agent';

    protected static ?string $navigationIcon = '';

    protected const int FINDER_PAGE_SIZE = 10;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return array_merge(
            $this->getModerationHeaderActions(),
            [
                static::getTestHeaderAction()
                    ->visible(Gatekeeper::isAdmin()),
                static::getExportTestsHeaderAction()
                    ->visible(Gatekeeper::isAdmin()),
                static::getAgentReplicateHeaderAction(),
                static::getTranslateAllAction(),
                static::getAnalyzeHeaderAction(AgentAnalytics::class),
                static::getAgentViewAction(),
            ],
            parent::getHeaderActions(),
            [
                LocaleSwitcher::make(),
            ]
        );
    }

    protected static function getTestHeaderAction(): Action
    {
        return Action::make('test')
            ->extraAttributes(['id' => btn_id('test'), 'class' => 'gap-0 2xl:gap-1.5'])
            ->label('Test')
            ->tooltip('Test')
            ->icon('heroicon-s-clipboard-document-check')
            ->labeledFrom('2xl')
            ->outlined()
            ->requiresConfirmation()
            ->form([
                Forms\Components\Radio::make('question_id')
                    ->label('Question')
                    ->options(function (Model $record) {
                        return $record->questions->pluck('question', 'id');
                    })
                    ->required(),
                static::getFormFieldTranslation()
                    ->required(),
                static::getFormFieldAgentLanguage()
                    ->required(),
            ])
            ->modalHeading(fn ($record) => "Test {$record->name}")
            ->modalIcon('heroicon-s-clipboard-document-check')
            ->modalDescription('A single response will be generated as a plain text file.')
            ->modalWidth('2xl')
            ->successNotificationTitle('Test response generated')
            ->after(static::getTestHeaderAfter())
            ->visible(fn ($record) => Gatekeeper::userCan('test', Agent::class, $record) && ! $record->is_locked);
    }

    protected static function getTestHeaderAfter(): Closure
    {
        return function (Agent $record, array $data, Component $livewire) {

            increase_timeout('HIGH');

            $agent = $livewire->form->getState();
            $client = new AgentClient(
                new Agent($agent),
                env('AGENT_API_BASE'),
                env('AGENT_API_TOKEN'),
                $agent['model_id'],
                env('AGENT_STRIP_SEQUENCE')
            );

            $systemPrompt = replace_tokens(
                $agent['model_system_prompt'],
                [
                    'translation' => config("config.translations.{$data['translation']}"),
                    'language' => config("config.languages.{$data['language']}"),
                ]
            );

            $question = AgentQuestion::find($data['question_id']);

            $response = $client->chat($question->question);

            $filename = Formatter::datetime(Carbon::now())." {$agent['name']}.txt";

            return response()->streamDownload(
                function () use ($response) {
                    echo $response;
                },
                $filename
            );

        };
    }

    protected static function getExportTestsHeaderAction(): Action
    {
        return Action::make('export_tests')
            ->extraAttributes(['id' => btn_id('export-tests'), 'class' => 'gap-0 2xl:gap-1.5'])
            ->label('Export Tests')
            ->tooltip('Export Tests')
            ->icon('heroicon-s-bars-arrow-up')
            ->outlined()
            ->labeledFrom('2xl')
            ->requiresConfirmation()
            ->form([
                Forms\Components\CheckboxList::make('question_ids')
                    ->label('Questions to Include')
                    ->options(function (Model $record) {
                        return $record->questions->pluck('question', 'id');
                    })
                    ->searchable()
                    ->bulkToggleable()
                    ->required(),
                Forms\Components\TextInput::make('num_responses')
                    ->label('# Responses per Question')
                    ->integer()
                    ->default(3)
                    ->minValue(1)
                    ->maxValue(10)
                    ->required(),
                static::getFormFieldTranslation()
                    ->required(),
                static::getFormFieldAgentLanguage()
                    ->required(),
            ])
            ->modalHeading(fn ($record) => "Export Tests for {$record->name}")
            ->modalIcon('heroicon-s-bars-arrow-up')
            ->modalDescription('
                A CSV of all selected questions will be downloaded.
                Note that the values in the current form will be used, not the last saved values.
                This allows you to test changes prior to saving them.
            ')
            ->modalWidth('2xl')
            ->successNotificationTitle('Tests exported')
            ->after(static::getExportTestsHeaderAfter())
            ->visible(fn ($record) => Gatekeeper::userCan('test', Agent::class, $record) && ! $record->is_locked);
    }

    protected static function getExportTestsHeaderAfter(): Closure
    {
        return function (Agent $record, array $data, Component $livewire) {

            increase_timeout('HIGH');

            $agent = $livewire->form->getState();
            $client = new AgentClient(
                new Agent($agent),
                env('AGENT_API_BASE'),
                env('AGENT_API_TOKEN'),
                $agent['model_id'],
                env('AGENT_STRIP_SEQUENCE')
            );

            $systemPrompt = replace_tokens(
                $agent['model_system_prompt'],
                [
                    'translation' => config("config.translations.{$data['translation']}"),
                    'language' => config("config.languages.{$data['language']}"),
                ]
            );

            $headers = [
                'Model',
                'Temperature',
                'Top P',
                'Max Tokens',
                'System Prompt',
                'Sample Question',
            ];
            for ($i = 0; $i <= $data['num_responses']; $i++) {
                $num = $i + 1;
                $headers[] = "Response {$num}";
            }

            $lines = [
                implode(
                    ',',
                    $headers
                ),
            ];

            foreach ($record->questions()->whereIn('id', $data['question_ids'])->get() as $question) {

                $line = [
                    $agent['model_id'],
                    $agent['model_temperature'],
                    $agent['model_top_p'],
                    $agent['model_max_tokens'],
                    wrap_csv_string($systemPrompt),
                    wrap_csv_string($question->question),
                ];

                for ($i = 0; $i <= $data['num_responses']; $i++) {
                    $line[] = wrap_csv_string($client->chat($question->question));
                }

                $lines[] = implode(
                    ',',
                    $line,
                );

            }

            $filename = Formatter::datetime(Carbon::now())." {$agent['name']}.csv";

            return response()->streamDownload(
                function () use ($lines) {
                    echo implode("\n", $lines);
                },
                $filename
            );

        };
    }

    protected static function getAgentReplicateHeaderAction(): Action
    {
        return static::getReplicateHeaderAction(
            [
                static::getFormFieldName()
                    ->required(),
                static::getFormFieldTeam()
                    ->visible(Gatekeeper::isAdmin()),
                Toggle::make('include_questions')
                    ->label('Include Questions'),
                Toggle::make('include_sources')
                    ->label('Include Sources'),
            ],
            ['slug', 'is_active', 'is_locked', 'vanity_domain', 'is_nonbillable']
        )
            ->extraAttributes(['id' => btn_id('duplicate'), 'class' => 'gap-0 2xl:gap-1.5'])
            ->label('Duplicate')
            ->tooltip('Duplicate')
            ->labeledFrom('2xl')
            ->after(function (Model $record, Model $replica, array $data): void {
                if ($data['include_questions']) {
                    $replica->questions()->createMany($record->questions->toArray());
                }
                if ($data['include_sources']) {
                    $replica->categories()->sync($record->categories()->pluck('categories.id')->toArray());
                    $replica->collections()->sync($record->collections()->pluck('collections.id')->toArray());
                    $replica->sources()->sync($record->sources()->pluck('sources.id')->toArray());
                }
            })
        ;
    }

    protected static function getTranslateAllAction(): Action
    {
        return Action::make('translate_all')
            ->extraAttributes(['id' => btn_id('translate-all'), 'class' => 'gap-0 2xl:gap-1.5'])
            ->label('Translate All')
            ->tooltip('Translate All')
            ->icon('heroicon-s-language')
            ->outlined()
            ->labeledFrom('2xl')
            ->requiresConfirmation()
            ->modalHeading('Translate All Fields from English')
            ->modalIcon('heroicon-s-language')
            ->modalDescription(new HtmlString("
                <strong>Important:</strong> All non-English translations will be overwritten by new translations if you proceed.
                This can't be undone.
            "))
            ->after(static::getTranslateAllHeaderAfter())
            ->visible(fn ($record) => Gatekeeper::userCan('update', Agent::class, $record) && ! $record->is_locked);
    }

    protected static function getTranslateAllHeaderAfter(): Closure
    {
        return function (Agent $record, Component $livewire) {

            increase_timeout('HIGH');

            $data = $livewire->form->getState();
            dispatch(
                new TranslateModelJob(
                    $record,
                    Agent::getSupportedLanguages(
                        $data['supported_languages'] ?? null,
                        $data['auto_translate'] ?? null,
                        $data['model_id'] ?? null,
                        $record
                    ),
                    ['questions']
                )
            );

            Notification::make()
                ->title('Translating all fields from English ...')
                ->body('All translatable fields for all supported languages are being translated in the background. This could take a few minutes.')
                ->icon('heroicon-s-language')
                ->send();

        };
    }

    protected static function getAgentViewAction(): Action
    {
        return static::$resource::decorateViewAction(
            Action::make('View'),
            fn ($record) => $record->is_active ? 'View Agent' : 'Agent Not Active',
            fn ($record) => $record->is_active,
            fn ($record) => $record->getUrl()
        );
    }

    public static function getFormFieldRecordFinder(
        string $relation,
        string $modelClass,
        Builder $baseQuery,
        Closure $columnsClosure,
        Closure $filtersClosure,
        Closure $recordLabelClosure = null,
        bool $public = false,
        string $label = null,
        string $description = null,
        string $buttonLabel = null
    ): RecordFinder {

        $modelLabel = Labeller::model($modelClass);
        $modelLabelPlural = Str::plural($modelLabel);

        if (is_null($label)) {
            $label = $modelLabelPlural;
        }
        if (is_null($description)) {
            $description = "Sources added to selected {$modelLabelPlural} will automatically be included.";
        }
        if (is_null($buttonLabel)) {
            $buttonLabel = "Select {$modelLabelPlural}";
        }

        return RecordFinder::make($relation)
            ->label($label)
            ->helperText($description)
            ->relationship($relation)
            ->multiple()
            ->standalone()
            ->query(function () use ($baseQuery, $public) {
                if (!$public) {
                    $baseQuery->privateOrContributed(get_current_team_id());
                }
                $baseQuery->orderBy('name', 'asc');
                return $baseQuery;
            })
            ->modifyTableUsing(function (Tables\Table $table) use ($columnsClosure, $filtersClosure) {
                $table->defaultPaginationPageOption(static::FINDER_PAGE_SIZE)
                    ->defaultSort('name', 'asc')
                    ->description(false)
                    ->paginated()
                    ->paginationPageOptions([static::FINDER_PAGE_SIZE])
                    ->columns($columnsClosure())
                    ->filters($filtersClosure())
                    ->filtersFormColumns(2)
                    ->actions([
                        static::decorateViewReferenceAction(Tables\Actions\Action::make('Reference')),
                    ])
                ;
            })
            ->getRecordLabelFromRecordUsing($recordLabelClosure)
            ->listWithLineBreaks()
            ->expandableLimitedList()
            ->bulleted()
            ->placeholder(false)
            ->openModalActionLabel($buttonLabel)
            ->openModalActionIcon(false)
            ->openModalActionColor('gray')
            ->openModalActionModalHeading("Select {$modelLabelPlural} to Include in Agent")
            ->openModalActionModalWidth(MaxWidth::SevenExtraLarge)
            ->openModalActionModalSubmitActionLabel("Confirm {$modelLabel} Selection")
            ;

    }

    public static function getTableFilterMyTeam(): TernaryFilter
    {
        return TernaryFilter::make('team_id')
            ->label('Contributing Team')
            ->placeholder('')
            ->trueLabel('My Team')
            ->falseLabel('Other Teams')
            ->queries(
                true: fn (Builder $query) => $query->where('team_id', get_current_team_id()),
                false: fn (Builder $query) => $query->where('team_id', '!=', get_current_team_id()),
                blank: fn (Builder $query) => $query,
            )
        ;
    }
    protected static function getTranslateActions(string $tab, array $fields): Forms\Components\Actions
    {
        $defaultLanguage = config('agent.languages')[env('AGENT_DEFAULT_LANGUAGE')];

        return Forms\Components\Actions::make([

            Forms\Components\Actions\Action::make("{$tab}_translate")
                ->extraAttributes(['id' => btn_id("translate-{$tab}")])
                ->label('Translate All')
                ->icon('heroicon-s-language')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields) {
                    $lang = $livewire->getActiveActionsLocale();
                    $frontend = $record->frontend;
                    if ($frontend) {
                        foreach ($fields as $field) {
                            $set(
                                $field,
                                $livewire->translateFormField(
                                    $lang,
                                    $get($field),
                                    $frontend->getTranslation($field, $lang)
                                )
                            );
                        }
                    }
                })
            ,

            Forms\Components\Actions\Action::make("{$tab}_reset_translation")
                ->extraAttributes(['id' => btn_id("reset-translation-{$tab}")])
                ->label("Reset All to {$defaultLanguage}")
                ->icon('heroicon-s-arrow-path')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields) {
                    $frontend = $record->frontend;
                    if ($frontend) {
                        foreach ($fields as $field) {
                            $set(
                                $field,
                                $frontend->getTranslation($field, env('AGENT_DEFAULT_LANGUAGE'))
                            );
                        }
                    }
                })
            ,

        ])
            ->alignment(Alignment::Center)
            ;
    }


}
